# QR Background API - Comprehensive Linux Deployment Guide

This guide provides complete instructions for deploying the QR Background API on Linux servers in production environments.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [System Preparation](#system-preparation)
3. [Installation](#installation)
4. [Configuration](#configuration)
5. [Service Setup](#service-setup)
6. [Security Configuration](#security-configuration)
7. [Monitoring and Logging](#monitoring-and-logging)
8. [Maintenance](#maintenance)
9. [Performance Tuning](#performance-tuning)
10. [Troubleshooting](#troubleshooting)

## Prerequisites

### System Requirements

**Minimum Requirements:**
- **OS**: Ubuntu 20.04+, CentOS 8+, RHEL 8+, Debian 11+
- **CPU**: 2 cores, 2.0 GHz
- **RAM**: 2GB
- **Storage**: 20GB available space
- **Network**: Stable internet connection

**Recommended for Production:**
- **CPU**: 4+ cores, 2.5+ GHz
- **RAM**: 8GB+
- **Storage**: 100GB+ SSD
- **Network**: 1Gbps+ connection

### Software Dependencies

**Required Packages:**
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install -y curl wget git build-essential systemd

# CentOS/RHEL/Rocky Linux
sudo dnf update
sudo dnf install -y curl wget git gcc systemd
```

**Go Installation:**
```bash
# Download and install Go 1.21+
GO_VERSION="1.21.5"
wget https://go.dev/dl/go${GO_VERSION}.linux-amd64.tar.gz
sudo rm -rf /usr/local/go
sudo tar -C /usr/local -xzf go${GO_VERSION}.linux-amd64.tar.gz

# Add Go to PATH
echo 'export PATH=$PATH:/usr/local/go/bin' | sudo tee -a /etc/profile
echo 'export GOPATH=$HOME/go' | sudo tee -a /etc/profile
echo 'export PATH=$PATH:$GOPATH/bin' | sudo tee -a /etc/profile
source /etc/profile

# Verify installation
go version
```

## System Preparation

### 1. Create System User

```bash
# Create dedicated service user
sudo useradd --system --shell /bin/false --home /opt/qr-background-api --create-home qr-api

# Create additional groups if needed
sudo groupadd qr-api-admin
sudo usermod -a -G qr-api-admin qr-api
```

### 2. Create Directory Structure

```bash
# Application directories
sudo mkdir -p /opt/qr-background-api/{bin,storage,temp,backup}
sudo mkdir -p /etc/qr-background-api
sudo mkdir -p /var/log/qr-background-api
sudo mkdir -p /var/lib/qr-background-api

# Set ownership
sudo chown -R qr-api:qr-api /opt/qr-background-api
sudo chown -R qr-api:qr-api /var/log/qr-background-api
sudo chown -R qr-api:qr-api /var/lib/qr-background-api
sudo chown root:qr-api /etc/qr-background-api

# Set permissions
sudo chmod 755 /opt/qr-background-api
sudo chmod 750 /etc/qr-background-api
sudo chmod 755 /var/log/qr-background-api
sudo chmod 755 /var/lib/qr-background-api
```

### 3. Configure System Limits

```bash
# Create limits configuration
sudo tee /etc/security/limits.d/qr-api.conf << 'EOF'
# QR Background API limits
qr-api soft nofile 65536
qr-api hard nofile 65536
qr-api soft nproc 32768
qr-api hard nproc 32768
qr-api soft memlock unlimited
qr-api hard memlock unlimited
EOF

# Configure systemd limits
sudo mkdir -p /etc/systemd/system.conf.d
sudo tee /etc/systemd/system.conf.d/qr-api.conf << 'EOF'
[Manager]
DefaultLimitNOFILE=65536
DefaultLimitNPROC=32768
EOF
```

## Installation

### 1. Clone and Build Application

```bash
# Clone repository
cd /tmp
git clone https://github.com/your-org/qr-background-api.git
cd qr-background-api

# Build application with optimizations
CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build \
  -ldflags="-s -w -X main.version=$(git describe --tags --always)" \
  -o qr-background-api cmd/server/main.go

# Install binary
sudo cp qr-background-api /opt/qr-background-api/bin/
sudo chown qr-api:qr-api /opt/qr-background-api/bin/qr-background-api
sudo chmod 755 /opt/qr-background-api/bin/qr-background-api

# Verify installation
/opt/qr-background-api/bin/qr-background-api --version
```

### 2. Install Configuration Files

```bash
# Copy production configuration
sudo cp config-production.yaml /etc/qr-background-api/config.yaml
sudo chown root:qr-api /etc/qr-background-api/config.yaml
sudo chmod 640 /etc/qr-background-api/config.yaml

# Create environment file for secrets
sudo tee /etc/qr-background-api/environment << 'EOF'
# QR Background API Environment Variables
# Cloud Storage Credentials (if using cloud storage)
QR_API_CLOUD_STORAGE_ACCESS_KEY=""
QR_API_CLOUD_STORAGE_SECRET_KEY=""

# API Keys (if using API key authentication)
QR_API_SECURITY_API_KEY=""

# Database credentials (if using external database)
QR_API_DATABASE_URL=""
QR_API_DATABASE_PASSWORD=""
EOF

sudo chown root:qr-api /etc/qr-background-api/environment
sudo chmod 600 /etc/qr-background-api/environment
```

## Configuration

### 1. Customize Production Configuration

Edit `/etc/qr-background-api/config.yaml`:

```bash
sudo nano /etc/qr-background-api/config.yaml
```

**Key settings to customize:**

```yaml
server:
  port: 8080                    # Change if port 8080 is in use

storage:
  local_path: "/opt/qr-background-api/storage"
  max_file_size: 20971520       # Adjust based on requirements

worker_pool:
  size: 8                       # Set to number of CPU cores

performance:
  memory_limit: 2147483648      # Adjust based on available RAM

logging:
  file_path: "/var/log/qr-background-api/api.log"
  level: "info"                 # Use "warn" or "error" for production

cloud_storage:
  enabled: false                # Enable if using cloud storage
  # Configure cloud storage settings if enabled
```

### 2. Environment Variables

Edit `/etc/qr-background-api/environment` to add sensitive configuration:

```bash
# Example cloud storage configuration
QR_API_CLOUD_STORAGE_ENABLED=true
QR_API_CLOUD_STORAGE_ACCESS_KEY="your-access-key"
QR_API_CLOUD_STORAGE_SECRET_KEY="your-secret-key"
QR_API_CLOUD_STORAGE_BUCKET="your-bucket-name"
QR_API_CLOUD_STORAGE_REGION="us-east-1"
```

### 3. Validate Configuration

```bash
# Test configuration
sudo -u qr-api /opt/qr-background-api/bin/qr-background-api \
  -config /etc/qr-background-api/config.yaml \
  -validate-config

# Test with environment variables
sudo -u qr-api env $(cat /etc/qr-background-api/environment | xargs) \
  /opt/qr-background-api/bin/qr-background-api \
  -config /etc/qr-background-api/config.yaml \
  -validate-config
```

## Service Setup

### 1. Create Systemd Service

```bash
sudo tee /etc/systemd/system/qr-background-api.service << 'EOF'
[Unit]
Description=QR Background API Server
Documentation=https://github.com/your-org/qr-background-api
After=network.target network-online.target
Wants=network-online.target
RequiresMountsFor=/opt/qr-background-api /var/log/qr-background-api

[Service]
Type=simple
User=qr-api
Group=qr-api

# Working directory
WorkingDirectory=/opt/qr-background-api

# Command to run
ExecStart=/opt/qr-background-api/bin/qr-background-api -config /etc/qr-background-api/config.yaml
ExecReload=/bin/kill -HUP $MAINPID

# Environment
EnvironmentFile=-/etc/qr-background-api/environment

# Process management
Restart=always
RestartSec=5
KillMode=mixed
KillSignal=SIGTERM
TimeoutStopSec=30

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/qr-background-api/storage /opt/qr-background-api/temp /var/log/qr-background-api

# Resource limits
LimitNOFILE=65536
LimitNPROC=32768
MemoryMax=4G
CPUQuota=400%

# Logging
StandardOutput=journal
StandardError=journal
SyslogIdentifier=qr-background-api

[Install]
WantedBy=multi-user.target
EOF
```

## Monitoring and Logging

### 1. Log Management

**View Application Logs:**
```bash
# Real-time logs
sudo tail -f /var/log/qr-background-api/api.log

# Structured log viewing with jq
sudo tail -f /var/log/qr-background-api/api.log | jq '.'

# Filter logs by level
sudo grep '"level":"error"' /var/log/qr-background-api/api.log | jq '.'

# View systemd logs
sudo journalctl -u qr-background-api -f --output=json-pretty
```

**Log Analysis Scripts:**
```bash
# Create log analysis script
sudo tee /usr/local/bin/qr-api-logs << 'EOF'
#!/bin/bash
LOG_FILE="/var/log/qr-background-api/api.log"

case "$1" in
    errors)
        echo "Recent errors:"
        grep '"level":"error"' "$LOG_FILE" | tail -20 | jq -r '.time + " " + .msg'
        ;;
    performance)
        echo "Slow requests (>1000ms):"
        grep '"processing_time_ms"' "$LOG_FILE" | jq 'select(.processing_time_ms > 1000)' | tail -10
        ;;
    stats)
        echo "Request statistics for today:"
        grep "$(date +%Y-%m-%d)" "$LOG_FILE" | grep '"handler"' | jq -r '.handler' | sort | uniq -c
        ;;
    *)
        echo "Usage: $0 {errors|performance|stats}"
        exit 1
        ;;
esac
EOF

sudo chmod +x /usr/local/bin/qr-api-logs
```

### 2. Health Monitoring

**Health Check Script:**
```bash
sudo tee /usr/local/bin/qr-api-health << 'EOF'
#!/bin/bash
API_URL="http://localhost:8080/health"
TIMEOUT=10

# Check API health
response=$(curl -s -w "%{http_code}" --max-time $TIMEOUT "$API_URL" -o /tmp/health_response)
http_code="${response: -3}"

if [ "$http_code" = "200" ]; then
    echo "✓ API is healthy"
    cat /tmp/health_response | jq '.' 2>/dev/null || cat /tmp/health_response
    exit 0
else
    echo "✗ API health check failed (HTTP $http_code)"
    cat /tmp/health_response
    exit 1
fi
EOF

sudo chmod +x /usr/local/bin/qr-api-health
```

**System Resource Monitoring:**
```bash
sudo tee /usr/local/bin/qr-api-monitor << 'EOF'
#!/bin/bash
echo "=== QR Background API System Monitor ==="
echo "Date: $(date)"
echo

# Service status
echo "Service Status:"
systemctl is-active qr-background-api
echo

# Process information
echo "Process Information:"
ps aux | grep qr-background-api | grep -v grep
echo

# Memory usage
echo "Memory Usage:"
pmap -x $(pgrep qr-background-api) | tail -1
echo

# Disk usage
echo "Storage Usage:"
du -sh /opt/qr-background-api/storage
df -h /opt/qr-background-api/storage
echo

# Network connections
echo "Network Connections:"
ss -tlnp | grep :8080
echo

# Recent errors
echo "Recent Errors (last 10):"
grep '"level":"error"' /var/log/qr-background-api/api.log | tail -10 | jq -r '.time + " " + .msg' 2>/dev/null || echo "No recent errors"
EOF

sudo chmod +x /usr/local/bin/qr-api-monitor
```

### 3. Automated Monitoring with Cron

```bash
# Create monitoring cron jobs
sudo tee /etc/cron.d/qr-background-api << 'EOF'
# QR Background API monitoring jobs
SHELL=/bin/bash
PATH=/usr/local/sbin:/usr/local/bin:/sbin:/bin:/usr/sbin:/usr/bin

# Health check every 5 minutes
*/5 * * * * root /usr/local/bin/qr-api-health > /dev/null 2>&1 || echo "QR API health check failed at $(date)" >> /var/log/qr-background-api/health.log

# System monitor every hour
0 * * * * root /usr/local/bin/qr-api-monitor >> /var/log/qr-background-api/monitor.log 2>&1

# Cleanup old logs weekly
0 2 * * 0 root find /var/log/qr-background-api -name "*.log" -mtime +30 -delete
EOF
```

## Maintenance

### 1. Backup Procedures

**Configuration Backup:**
```bash
sudo tee /usr/local/bin/qr-api-backup-config << 'EOF'
#!/bin/bash
BACKUP_DIR="/backup/qr-api/config"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p "$BACKUP_DIR"

# Backup configuration files
tar -czf "$BACKUP_DIR/config_$DATE.tar.gz" \
    /etc/qr-background-api/ \
    /etc/systemd/system/qr-background-api.service \
    /etc/nginx/sites-available/qr-background-api 2>/dev/null

echo "Configuration backup created: $BACKUP_DIR/config_$DATE.tar.gz"

# Keep only last 30 backups
find "$BACKUP_DIR" -name "config_*.tar.gz" -mtime +30 -delete
EOF

sudo chmod +x /usr/local/bin/qr-api-backup-config
```

**Data Backup:**
```bash
sudo tee /usr/local/bin/qr-api-backup-data << 'EOF'
#!/bin/bash
BACKUP_DIR="/backup/qr-api/data"
DATE=$(date +%Y%m%d_%H%M%S)
STORAGE_DIR="/opt/qr-background-api/storage"

mkdir -p "$BACKUP_DIR"

# Create incremental backup
rsync -av --link-dest="$BACKUP_DIR/latest" \
    "$STORAGE_DIR/" \
    "$BACKUP_DIR/backup_$DATE/"

# Update latest symlink
rm -f "$BACKUP_DIR/latest"
ln -s "backup_$DATE" "$BACKUP_DIR/latest"

echo "Data backup created: $BACKUP_DIR/backup_$DATE"

# Keep only last 7 daily backups
find "$BACKUP_DIR" -maxdepth 1 -name "backup_*" -mtime +7 -exec rm -rf {} \;
EOF

sudo chmod +x /usr/local/bin/qr-api-backup-data
```

### 2. Update Procedures

**Application Update Script:**
```bash
sudo tee /usr/local/bin/qr-api-update << 'EOF'
#!/bin/bash
set -e

REPO_URL="https://github.com/your-org/qr-background-api.git"
BUILD_DIR="/tmp/qr-api-update"
BINARY_PATH="/opt/qr-background-api/bin/qr-background-api"
BACKUP_PATH="/opt/qr-background-api/bin/qr-background-api.backup"

echo "Starting QR Background API update..."

# Backup current binary
cp "$BINARY_PATH" "$BACKUP_PATH"
echo "Current binary backed up to $BACKUP_PATH"

# Clone latest version
rm -rf "$BUILD_DIR"
git clone "$REPO_URL" "$BUILD_DIR"
cd "$BUILD_DIR"

# Build new version
echo "Building new version..."
CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build \
  -ldflags="-s -w -X main.version=$(git describe --tags --always)" \
  -o qr-background-api cmd/server/main.go

# Test new binary
echo "Testing new binary..."
./qr-background-api -version

# Stop service
echo "Stopping service..."
systemctl stop qr-background-api

# Install new binary
cp qr-background-api "$BINARY_PATH"
chown qr-api:qr-api "$BINARY_PATH"
chmod 755 "$BINARY_PATH"

# Start service
echo "Starting service..."
systemctl start qr-background-api

# Wait for service to be ready
sleep 5

# Health check
if /usr/local/bin/qr-api-health; then
    echo "✓ Update successful!"
    rm -rf "$BUILD_DIR"
else
    echo "✗ Health check failed, rolling back..."
    systemctl stop qr-background-api
    cp "$BACKUP_PATH" "$BINARY_PATH"
    systemctl start qr-background-api
    echo "Rollback completed"
    exit 1
fi
EOF

sudo chmod +x /usr/local/bin/qr-api-update
```

### 3. Maintenance Tasks

**Weekly Maintenance Script:**
```bash
sudo tee /usr/local/bin/qr-api-maintenance << 'EOF'
#!/bin/bash
echo "=== QR Background API Weekly Maintenance ==="
echo "Date: $(date)"

# Backup configuration
echo "Backing up configuration..."
/usr/local/bin/qr-api-backup-config

# Backup data
echo "Backing up data..."
/usr/local/bin/qr-api-backup-data

# Clean up old logs
echo "Cleaning up old logs..."
find /var/log/qr-background-api -name "*.log.*" -mtime +30 -delete

# Clean up temporary files
echo "Cleaning up temporary files..."
find /opt/qr-background-api/temp -type f -mtime +1 -delete

# Restart service for memory cleanup
echo "Restarting service for memory cleanup..."
systemctl restart qr-background-api

# Wait for service to be ready
sleep 10

# Health check
echo "Performing health check..."
/usr/local/bin/qr-api-health

echo "Maintenance completed successfully"
EOF

sudo chmod +x /usr/local/bin/qr-api-maintenance

# Schedule weekly maintenance
echo "0 3 * * 0 root /usr/local/bin/qr-api-maintenance >> /var/log/qr-background-api/maintenance.log 2>&1" | sudo tee -a /etc/cron.d/qr-background-api
```

## Performance Tuning

### 1. System-Level Optimizations

**Kernel Parameters:**
```bash
sudo tee /etc/sysctl.d/99-qr-api.conf << 'EOF'
# Network optimizations
net.core.somaxconn = 65535
net.core.netdev_max_backlog = 5000
net.ipv4.tcp_max_syn_backlog = 65535
net.ipv4.tcp_fin_timeout = 30
net.ipv4.tcp_keepalive_time = 1200
net.ipv4.tcp_keepalive_probes = 3
net.ipv4.tcp_keepalive_intvl = 15

# Memory optimizations
vm.swappiness = 10
vm.dirty_ratio = 15
vm.dirty_background_ratio = 5

# File system optimizations
fs.file-max = 2097152
fs.inotify.max_user_watches = 524288
EOF

sudo sysctl -p /etc/sysctl.d/99-qr-api.conf
```

**CPU Governor:**
```bash
# Set CPU governor to performance
echo 'performance' | sudo tee /sys/devices/system/cpu/cpu*/cpufreq/scaling_governor

# Make permanent
sudo tee /etc/systemd/system/cpu-performance.service << 'EOF'
[Unit]
Description=Set CPU governor to performance
After=multi-user.target

[Service]
Type=oneshot
ExecStart=/bin/bash -c 'echo performance | tee /sys/devices/system/cpu/cpu*/cpufreq/scaling_governor'
RemainAfterExit=yes

[Install]
WantedBy=multi-user.target
EOF

sudo systemctl enable cpu-performance.service
```

### 2. Application-Level Tuning

**High-Performance Configuration:**
```yaml
# Add to /etc/qr-background-api/config.yaml
server:
  read_timeout: 15
  write_timeout: 15
  idle_timeout: 60

performance:
  memory_limit: 4294967296      # 4GB
  gc_interval_seconds: 15
  stream_buffer_size: 131072    # 128KB
  max_stream_memory: 52428800   # 50MB

worker_pool:
  size: 16                      # 2x CPU cores
  queue_size: 320               # 20x worker count
  buffer_pool_size: 200

timeouts:
  qr_generation: 5
  image_process: 10
  storage_op: 15
```

### 3. Database and Storage Optimization

**Storage Mount Options:**
```bash
# Add to /etc/fstab for SSD storage
/dev/sdb1 /opt/qr-background-api/storage ext4 defaults,noatime,discard 0 2

# Remount with optimized options
sudo mount -o remount,noatime,discard /opt/qr-background-api/storage
```

## Troubleshooting

### 1. Common Issues and Solutions

**Service Won't Start:**
```bash
# Check service status
sudo systemctl status qr-background-api

# Check configuration
sudo -u qr-api /opt/qr-background-api/bin/qr-background-api \
  -config /etc/qr-background-api/config.yaml -validate-config

# Check permissions
ls -la /opt/qr-background-api/bin/qr-background-api
ls -la /etc/qr-background-api/config.yaml

# Check logs
sudo journalctl -u qr-background-api -n 50
```

**High Memory Usage:**
```bash
# Check memory usage
ps aux | grep qr-background-api
pmap -x $(pgrep qr-background-api)

# Adjust memory limits in config.yaml
performance:
  memory_limit: 2147483648      # Reduce to 2GB
  gc_interval_seconds: 10       # More frequent GC
  force_gc_threshold: 1073741824 # Lower GC threshold
```

**Slow Response Times:**
```bash
# Check worker pool utilization
grep "worker_pool" /var/log/qr-background-api/api.log | tail -20

# Increase worker pool size
worker_pool:
  size: 20                      # Increase workers
  queue_size: 400               # Increase queue

# Check disk I/O
iostat -x 1 5
```

**File Upload Failures:**
```bash
# Check disk space
df -h /opt/qr-background-api/storage

# Check file permissions
ls -la /opt/qr-background-api/storage

# Check file size limits
grep "max_file_size" /etc/qr-background-api/config.yaml
```

### 2. Diagnostic Commands

**System Diagnostics:**
```bash
# Create diagnostic script
sudo tee /usr/local/bin/qr-api-diagnose << 'EOF'
#!/bin/bash
echo "=== QR Background API Diagnostics ==="
echo "Date: $(date)"
echo

echo "Service Status:"
systemctl status qr-background-api --no-pager
echo

echo "Process Information:"
ps aux | grep qr-background-api | grep -v grep
echo

echo "Memory Usage:"
free -h
echo "Application Memory:"
pmap -x $(pgrep qr-background-api) 2>/dev/null | tail -1 || echo "Process not found"
echo

echo "Disk Usage:"
df -h /opt/qr-background-api/storage
echo

echo "Network Connections:"
ss -tlnp | grep :8080
echo

echo "Recent Logs (last 20 lines):"
tail -20 /var/log/qr-background-api/api.log
echo

echo "Configuration Test:"
sudo -u qr-api /opt/qr-background-api/bin/qr-background-api \
  -config /etc/qr-background-api/config.yaml -validate-config
EOF

sudo chmod +x /usr/local/bin/qr-api-diagnose
```

### 3. Emergency Procedures

**Emergency Restart:**
```bash
# Force restart if service is unresponsive
sudo systemctl kill qr-background-api
sudo systemctl start qr-background-api
```

**Emergency Cleanup:**
```bash
# Clean up storage if disk is full
sudo find /opt/qr-background-api/storage -type f -mtime +1 -delete
sudo find /opt/qr-background-api/temp -type f -delete
```

**Rollback to Previous Version:**
```bash
# If backup exists
sudo systemctl stop qr-background-api
sudo cp /opt/qr-background-api/bin/qr-background-api.backup \
       /opt/qr-background-api/bin/qr-background-api
sudo systemctl start qr-background-api
```

---

## Quick Reference

**Essential Commands:**
```bash
# Service management
sudo systemctl {start|stop|restart|status} qr-background-api

# View logs
sudo journalctl -u qr-background-api -f
sudo tail -f /var/log/qr-background-api/api.log

# Health check
/usr/local/bin/qr-api-health

# System monitoring
/usr/local/bin/qr-api-monitor

# Diagnostics
/usr/local/bin/qr-api-diagnose

# Maintenance
/usr/local/bin/qr-api-maintenance

# Update application
/usr/local/bin/qr-api-update
```

**Important Paths:**
- Binary: `/opt/qr-background-api/bin/qr-background-api`
- Config: `/etc/qr-background-api/config.yaml`
- Logs: `/var/log/qr-background-api/api.log`
- Storage: `/opt/qr-background-api/storage`
- Service: `/etc/systemd/system/qr-background-api.service`

For additional support, check the project documentation or create an issue in the GitHub repository.

### 2. Enable and Start Service

```bash
# Reload systemd configuration
sudo systemctl daemon-reload

# Enable service to start on boot
sudo systemctl enable qr-background-api

# Start the service
sudo systemctl start qr-background-api

# Check service status
sudo systemctl status qr-background-api

# View service logs
sudo journalctl -u qr-background-api -f
```

### 3. Service Management Commands

```bash
# Start service
sudo systemctl start qr-background-api

# Stop service
sudo systemctl stop qr-background-api

# Restart service
sudo systemctl restart qr-background-api

# Reload configuration (graceful restart)
sudo systemctl reload qr-background-api

# Check service status
sudo systemctl status qr-background-api

# View logs
sudo journalctl -u qr-background-api -n 100 -f

# View logs for specific time period
sudo journalctl -u qr-background-api --since "2024-01-01 00:00:00" --until "2024-01-01 23:59:59"
```

## Security Configuration

### 1. Firewall Configuration

**UFW (Ubuntu/Debian):**
```bash
# Enable UFW
sudo ufw enable

# Allow SSH (adjust port if needed)
sudo ufw allow 22/tcp

# Allow API port
sudo ufw allow 8080/tcp

# Allow from specific networks only (recommended)
sudo ufw allow from 10.0.0.0/8 to any port 8080
sudo ufw allow from **********/12 to any port 8080
sudo ufw allow from ***********/16 to any port 8080

# Check status
sudo ufw status verbose
```

**firewalld (CentOS/RHEL):**
```bash
# Enable firewalld
sudo systemctl enable --now firewalld

# Allow API port
sudo firewall-cmd --permanent --add-port=8080/tcp

# Allow from specific networks only
sudo firewall-cmd --permanent --add-rich-rule='rule family="ipv4" source address="10.0.0.0/8" port protocol="tcp" port="8080" accept'

# Reload configuration
sudo firewall-cmd --reload

# Check status
sudo firewall-cmd --list-all
```

### 2. SSL/TLS Configuration (Reverse Proxy)

**Nginx Configuration:**
```bash
# Install Nginx
sudo apt install nginx  # Ubuntu/Debian
sudo dnf install nginx  # CentOS/RHEL

# Create configuration
sudo tee /etc/nginx/sites-available/qr-background-api << 'EOF'
upstream qr_backend {
    server 127.0.0.1:8080;
    keepalive 32;
}

server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    # SSL configuration
    ssl_certificate /etc/ssl/certs/your-domain.crt;
    ssl_certificate_key /etc/ssl/private/your-domain.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # Security headers
    add_header Strict-Transport-Security "max-age=63072000" always;
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;

    # Proxy settings
    location / {
        proxy_pass http://qr_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Timeouts
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        # File upload settings
        client_max_body_size 50M;
    }

    # Health check endpoint
    location /health {
        proxy_pass http://qr_backend/health;
        access_log off;
    }
}
EOF

# Enable site
sudo ln -s /etc/nginx/sites-available/qr-background-api /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

### 3. File Permissions and Security

```bash
# Secure configuration files
sudo chmod 640 /etc/qr-background-api/config.yaml
sudo chmod 600 /etc/qr-background-api/environment

# Secure storage directories
sudo chmod 755 /opt/qr-background-api/storage
sudo chmod 750 /opt/qr-background-api/temp

# Set up log rotation
sudo tee /etc/logrotate.d/qr-background-api << 'EOF'
/var/log/qr-background-api/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 qr-api qr-api
    postrotate
        systemctl reload qr-background-api
    endscript
}
EOF
```
